import React from 'react';
import './Plant.css';

interface PlantState {
  health: number;
  water: number;
  nutrition: number;
  growth: number;
  stage: 'seed' | 'sprout' | 'growing' | 'flowering' | 'mature';
  lastWatered: number;
  lastFertilized: number;
}

interface PlantProps {
  state: PlantState;
}

const Plant: React.FC<PlantProps> = ({ state }) => {
  const getPlantEmoji = () => {
    switch (state.stage) {
      case 'seed': return '🌰';
      case 'sprout': return '🌱';
      case 'growing': return '🌿';
      case 'flowering': return '🌸';
      case 'mature': return '🌳';
      default: return '🌱';
    }
  };

  const getHealthColor = () => {
    if (state.health >= 80) return '#4CAF50';
    if (state.health >= 60) return '#FFC107';
    if (state.health >= 40) return '#FF9800';
    return '#F44336';
  };

  const getPlantSize = () => {
    const baseSize = 60;
    const growthMultiplier = 1 + (state.growth / 100) * 1.5;
    return baseSize * growthMultiplier;
  };

  const getPlantAnimation = () => {
    if (state.health < 30) return 'plant-sick';
    if (state.water > 80) return 'plant-happy';
    return 'plant-normal';
  };

  return (
    <div className="plant-wrapper">
      <div className="plant-pot">
        <div className="pot-base"></div>
        <div className="soil"></div>
      </div>
      
      <div 
        className={`plant ${getPlantAnimation()}`}
        style={{ 
          fontSize: `${getPlantSize()}px`,
          filter: `hue-rotate(${state.health < 50 ? '20deg' : '0deg'})`,
        }}
      >
        {getPlantEmoji()}
      </div>

      {/* 状态指示器 */}
      <div className="status-indicators">
        <div className="status-bar">
          <span className="status-label">💚</span>
          <div className="progress-bar">
            <div 
              className="progress-fill health"
              style={{ 
                width: `${state.health}%`,
                backgroundColor: getHealthColor()
              }}
            ></div>
          </div>
          <span className="status-value">{Math.round(state.health)}</span>
        </div>

        <div className="status-bar">
          <span className="status-label">💧</span>
          <div className="progress-bar">
            <div 
              className="progress-fill water"
              style={{ width: `${state.water}%` }}
            ></div>
          </div>
          <span className="status-value">{Math.round(state.water)}</span>
        </div>

        <div className="status-bar">
          <span className="status-label">🌱</span>
          <div className="progress-bar">
            <div 
              className="progress-fill nutrition"
              style={{ width: `${state.nutrition}%` }}
            ></div>
          </div>
          <span className="status-value">{Math.round(state.nutrition)}</span>
        </div>

        <div className="status-bar">
          <span className="status-label">📈</span>
          <div className="progress-bar">
            <div 
              className="progress-fill growth"
              style={{ width: `${state.growth}%` }}
            ></div>
          </div>
          <span className="status-value">{Math.round(state.growth)}</span>
        </div>
      </div>

      {/* 植物阶段显示 */}
      <div className="plant-stage">
        阶段: {
          state.stage === 'seed' ? '种子' :
          state.stage === 'sprout' ? '幼苗' :
          state.stage === 'growing' ? '生长' :
          state.stage === 'flowering' ? '开花' :
          '成熟'
        }
      </div>

      {/* 特效 */}
      {state.water > 90 && (
        <div className="water-drops">
          <span className="drop">💧</span>
          <span className="drop">💧</span>
          <span className="drop">💧</span>
        </div>
      )}

      {state.stage === 'flowering' && (
        <div className="sparkles">
          <span className="sparkle">✨</span>
          <span className="sparkle">✨</span>
          <span className="sparkle">✨</span>
        </div>
      )}
    </div>
  );
};

export default Plant;
