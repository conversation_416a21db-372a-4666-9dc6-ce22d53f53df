.weather-info {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  min-width: 200px;
  z-index: 10;
}

.weather-info.loading {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 16px;
}

.weather-main {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.weather-icon {
  font-size: 32px;
  animation: weatherPulse 3s ease-in-out infinite;
}

@keyframes weatherPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.weather-details {
  flex: 1;
}

.temperature {
  font-size: 24px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 4px;
}

.condition {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.weather-secondary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-top: 8px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.humidity, .location {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

.humidity-icon, .location-icon {
  font-size: 14px;
}

.weather-impact {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding-top: 8px;
}

.impact-good, .impact-bad {
  font-size: 11px;
  padding: 4px 8px;
  border-radius: 12px;
  margin-bottom: 4px;
  font-weight: 500;
}

.impact-good {
  background: rgba(76, 175, 80, 0.1);
  color: #2E7D32;
  border: 1px solid rgba(76, 175, 80, 0.2);
}

.impact-bad {
  background: rgba(244, 67, 54, 0.1);
  color: #C62828;
  border: 1px solid rgba(244, 67, 54, 0.2);
}

.weather-text {
  font-size: 14px;
  color: #666;
}

/* 天气图标动画 */
.weather-icon {
  position: relative;
}

/* 太阳动画 */
.weather-info:has(.weather-icon:contains("☀️")) .weather-icon {
  animation: sunRotate 10s linear infinite;
}

@keyframes sunRotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 雨滴动画 */
.weather-info:has(.weather-icon:contains("🌧️")) .weather-icon::after {
  content: '💧💧💧';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  font-size: 8px;
  animation: rainDrop 1.5s ease-in-out infinite;
}

@keyframes rainDrop {
  0% { 
    opacity: 1; 
    transform: translateX(-50%) translateY(0); 
  }
  100% { 
    opacity: 0; 
    transform: translateX(-50%) translateY(20px); 
  }
}

/* 云朵飘动 */
.weather-info:has(.weather-icon:contains("☁️")) .weather-icon {
  animation: cloudFloat 6s ease-in-out infinite;
}

@keyframes cloudFloat {
  0%, 100% { transform: translateX(0); }
  50% { transform: translateX(3px); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .weather-info {
    top: 10px;
    left: 10px;
    padding: 12px;
    min-width: 160px;
  }
  
  .weather-icon {
    font-size: 28px;
  }
  
  .temperature {
    font-size: 20px;
  }
  
  .condition {
    font-size: 12px;
  }
  
  .humidity, .location {
    font-size: 11px;
  }
  
  .impact-good, .impact-bad {
    font-size: 10px;
    padding: 3px 6px;
  }
}

/* 毛玻璃效果增强 */
@supports (backdrop-filter: blur(20px)) {
  .weather-info {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
  }
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .weather-info {
    background: rgba(30, 30, 30, 0.9);
    color: #fff;
  }
  
  .condition, .humidity, .location, .weather-text {
    color: #ccc;
  }
  
  .weather-secondary {
    border-top-color: rgba(255, 255, 255, 0.1);
  }
  
  .weather-impact {
    border-top-color: rgba(255, 255, 255, 0.1);
  }
}
