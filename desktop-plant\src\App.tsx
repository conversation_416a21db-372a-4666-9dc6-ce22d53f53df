import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import Plant from './components/Plant';
import WeatherInfo from './components/WeatherInfo';
import Controls from './components/Controls';
import './App.css';

interface PlantState {
  health: number;
  water: number;
  nutrition: number;
  growth: number;
  stage: 'seed' | 'sprout' | 'growing' | 'flowering' | 'mature';
  lastWatered: number;
  lastFertilized: number;
}

interface WeatherData {
  temperature: number;
  humidity: number;
  condition: string;
  location: string;
}

function App() {
  const [plantState, setPlantState] = useState<PlantState>({
    health: 80,
    water: 60,
    nutrition: 50,
    growth: 25,
    stage: 'sprout',
    lastWatered: Date.now(),
    lastFertilized: Date.now() - 86400000, // 1 day ago
  });

  const [weather, setWeather] = useState<WeatherData | null>(null);
  const [currentTime, setCurrentTime] = useState(new Date());

  // 更新时间
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // 获取天气数据
  useEffect(() => {
    const fetchWeather = async () => {
      try {
        const weatherData = await invoke<WeatherData>('get_weather');
        setWeather(weatherData);
      } catch (error) {
        console.error('Failed to fetch weather:', error);
        // 使用模拟数据
        setWeather({
          temperature: 22,
          humidity: 65,
          condition: 'sunny',
          location: '本地'
        });
      }
    };

    fetchWeather();
    const weatherTimer = setInterval(fetchWeather, 300000); // 每5分钟更新一次
    return () => clearInterval(weatherTimer);
  }, []);

  // 植物状态更新逻辑
  useEffect(() => {
    const updatePlant = () => {
      setPlantState(prev => {
        const now = Date.now();
        const timeSinceWatered = now - prev.lastWatered;
        const timeSinceFertilized = now - prev.lastFertilized;
        
        // 水分消耗 (每小时消耗1-2点)
        const waterDecay = Math.min(timeSinceWatered / (1000 * 60 * 60) * 1.5, 5);
        
        // 营养消耗 (每天消耗5-10点)
        const nutritionDecay = Math.min(timeSinceFertilized / (1000 * 60 * 60 * 24) * 7, 10);
        
        // 天气影响
        let weatherBonus = 0;
        if (weather) {
          if (weather.temperature >= 18 && weather.temperature <= 26) weatherBonus += 2;
          if (weather.humidity >= 40 && weather.humidity <= 70) weatherBonus += 2;
          if (weather.condition === 'sunny') weatherBonus += 3;
        }
        
        const newWater = Math.max(0, prev.water - waterDecay);
        const newNutrition = Math.max(0, prev.nutrition - nutritionDecay);
        
        // 健康度计算
        let healthChange = 0;
        if (newWater < 20) healthChange -= 2;
        if (newNutrition < 20) healthChange -= 2;
        if (newWater > 40 && newNutrition > 40) healthChange += 1;
        healthChange += weatherBonus * 0.5;
        
        const newHealth = Math.max(0, Math.min(100, prev.health + healthChange));
        
        // 生长计算
        let growthChange = 0;
        if (newHealth > 60 && newWater > 30 && newNutrition > 30) {
          growthChange = 0.5 + weatherBonus * 0.2;
        }
        
        const newGrowth = Math.min(100, prev.growth + growthChange);
        
        // 阶段更新
        let newStage = prev.stage;
        if (newGrowth >= 80 && prev.stage !== 'mature') newStage = 'mature';
        else if (newGrowth >= 60 && prev.stage !== 'flowering') newStage = 'flowering';
        else if (newGrowth >= 30 && prev.stage !== 'growing') newStage = 'growing';
        else if (newGrowth >= 10 && prev.stage !== 'sprout') newStage = 'sprout';
        
        return {
          ...prev,
          health: newHealth,
          water: newWater,
          nutrition: newNutrition,
          growth: newGrowth,
          stage: newStage,
        };
      });
    };

    const plantTimer = setInterval(updatePlant, 10000); // 每10秒更新一次
    return () => clearInterval(plantTimer);
  }, [weather]);

  const handleWater = () => {
    setPlantState(prev => ({
      ...prev,
      water: Math.min(100, prev.water + 20),
      lastWatered: Date.now(),
    }));
  };

  const handleFertilize = () => {
    setPlantState(prev => ({
      ...prev,
      nutrition: Math.min(100, prev.nutrition + 30),
      lastFertilized: Date.now(),
    }));
  };

  return (
    <div className="app">
      <div className="time-display">
        {currentTime.toLocaleTimeString()}
      </div>
      
      <WeatherInfo weather={weather} />
      
      <div className="plant-container">
        <Plant state={plantState} />
      </div>
      
      <Controls 
        onWater={handleWater}
        onFertilize={handleFertilize}
        plantState={plantState}
      />
    </div>
  );
}

export default App;
