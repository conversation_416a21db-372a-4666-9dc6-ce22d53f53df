.plant-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  padding: 20px;
}

.plant-pot {
  position: relative;
  margin-bottom: -10px;
  z-index: 1;
}

.pot-base {
  width: 80px;
  height: 60px;
  background: linear-gradient(145deg, #8B4513, #A0522D);
  border-radius: 0 0 40px 40px;
  position: relative;
  box-shadow: 
    inset 0 2px 4px rgba(0, 0, 0, 0.3),
    0 4px 8px rgba(0, 0, 0, 0.2);
}

.pot-base::before {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  height: 10px;
  background: linear-gradient(145deg, #A0522D, #8B4513);
  border-radius: 50px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.soil {
  position: absolute;
  top: -8px;
  left: 5px;
  right: 5px;
  height: 15px;
  background: linear-gradient(145deg, #4A4A4A, #654321);
  border-radius: 50px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
}

.plant {
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
  cursor: pointer;
  user-select: none;
}

.plant:hover {
  transform: scale(1.1);
}

/* 植物动画 */
@keyframes plant-normal {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-2px) rotate(1deg); }
}

@keyframes plant-happy {
  0%, 100% { transform: translateY(0) rotate(0deg) scale(1); }
  25% { transform: translateY(-3px) rotate(-2deg) scale(1.05); }
  75% { transform: translateY(-3px) rotate(2deg) scale(1.05); }
}

@keyframes plant-sick {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(1px) rotate(-1deg); }
}

.plant-normal {
  animation: plant-normal 4s ease-in-out infinite;
}

.plant-happy {
  animation: plant-happy 2s ease-in-out infinite;
}

.plant-sick {
  animation: plant-sick 3s ease-in-out infinite;
  filter: grayscale(30%);
}

/* 状态指示器 */
.status-indicators {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 200px;
}

.status-bar {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.9);
  padding: 6px 12px;
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.status-label {
  font-size: 16px;
  min-width: 20px;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-fill.health {
  background: linear-gradient(90deg, #4CAF50, #8BC34A);
}

.progress-fill.water {
  background: linear-gradient(90deg, #2196F3, #03DAC6);
}

.progress-fill.nutrition {
  background: linear-gradient(90deg, #FF9800, #FFC107);
}

.progress-fill.growth {
  background: linear-gradient(90deg, #9C27B0, #E91E63);
}

.status-value {
  font-size: 12px;
  font-weight: 600;
  color: #666;
  min-width: 30px;
  text-align: right;
}

/* 植物阶段显示 */
.plant-stage {
  margin-top: 15px;
  background: rgba(255, 255, 255, 0.9);
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

/* 特效 */
.water-drops {
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  pointer-events: none;
}

.drop {
  position: absolute;
  font-size: 12px;
  animation: dropFall 2s ease-in-out infinite;
}

.drop:nth-child(1) { left: -10px; animation-delay: 0s; }
.drop:nth-child(2) { left: 0px; animation-delay: 0.3s; }
.drop:nth-child(3) { left: 10px; animation-delay: 0.6s; }

@keyframes dropFall {
  0% { 
    opacity: 1; 
    transform: translateY(0); 
  }
  100% { 
    opacity: 0; 
    transform: translateY(30px); 
  }
}

.sparkles {
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  pointer-events: none;
  width: 60px;
  height: 60px;
}

.sparkle {
  position: absolute;
  font-size: 14px;
  animation: sparkleFloat 3s ease-in-out infinite;
}

.sparkle:nth-child(1) { 
  top: 10px; 
  left: 10px; 
  animation-delay: 0s; 
}
.sparkle:nth-child(2) { 
  top: 5px; 
  right: 10px; 
  animation-delay: 1s; 
}
.sparkle:nth-child(3) { 
  bottom: 10px; 
  left: 50%; 
  transform: translateX(-50%);
  animation-delay: 2s; 
}

@keyframes sparkleFloat {
  0%, 100% {
    opacity: 0.3;
    transform: translateY(0) scale(0.8);
  }
  50% {
    opacity: 1;
    transform: translateY(-10px) scale(1.2);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .plant-wrapper {
    padding: 10px;
  }

  .status-indicators {
    min-width: 150px;
  }

  .status-bar {
    padding: 4px 8px;
    gap: 6px;
  }

  .status-label {
    font-size: 14px;
  }

  .plant-stage {
    font-size: 12px;
    padding: 6px 12px;
  }
}
