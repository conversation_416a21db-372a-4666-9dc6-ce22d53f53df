{"version": 3, "names": ["_getPrototypeOf", "o", "exports", "default", "Object", "setPrototypeOf", "getPrototypeOf", "bind", "__proto__"], "sources": ["../../src/helpers/getPrototypeOf.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nexport default function _getPrototypeOf(o: object): any {\n  // @ts-expect-error explicitly assign to function\n  _getPrototypeOf = Object.setPrototypeOf\n    ? // @ts-expect-error -- intentionally omitting the argument\n      Object.getPrototypeOf.bind(/* undefined */)\n    : function _getPrototypeOf<T extends object>(o: T) {\n        return (o as any).__proto__ || Object.getPrototypeOf(o);\n      };\n  return _getPrototypeOf(o);\n}\n"], "mappings": ";;;;;;AAEe,SAASA,eAAeA,CAACC,CAAS,EAAO;EAEtDC,OAAA,CAAAC,OAAA,GAAAH,eAAe,GAAGI,MAAM,CAACC,cAAc,GAEnCD,MAAM,CAACE,cAAc,CAACC,IAAI,CAAgB,CAAC,GAC3C,SAASP,eAAeA,CAAmBC,CAAI,EAAE;IAC/C,OAAQA,CAAC,CAASO,SAAS,IAAIJ,MAAM,CAACE,cAAc,CAACL,CAAC,CAAC;EACzD,CAAC;EACL,OAAOD,eAAe,CAACC,CAAC,CAAC;AAC3B", "ignoreList": []}