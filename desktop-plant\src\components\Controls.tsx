import React, { useState } from 'react';
import './Controls.css';

interface PlantState {
  health: number;
  water: number;
  nutrition: number;
  growth: number;
  stage: 'seed' | 'sprout' | 'growing' | 'flowering' | 'mature';
  lastWatered: number;
  lastFertilized: number;
}

interface ControlsProps {
  onWater: () => void;
  onFertilize: () => void;
  plantState: PlantState;
}

const Controls: React.FC<ControlsProps> = ({ onWater, onFertilize, plantState }) => {
  const [showDetails, setShowDetails] = useState(false);

  const canWater = () => {
    return plantState.water < 90;
  };

  const canFertilize = () => {
    const timeSinceFertilized = Date.now() - plantState.lastFertilized;
    const hoursSinceFertilized = timeSinceFertilized / (1000 * 60 * 60);
    return hoursSinceFertilized >= 2 && plantState.nutrition < 80; // 至少2小时间隔
  };

  const getTimeSinceLastAction = (timestamp: number) => {
    const diff = Date.now() - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days}天前`;
    if (hours > 0) return `${hours}小时前`;
    if (minutes > 0) return `${minutes}分钟前`;
    return '刚刚';
  };

  const getPlantAdvice = () => {
    const advice = [];
    
    if (plantState.water < 30) {
      advice.push('🚰 植物需要浇水了！');
    }
    
    if (plantState.nutrition < 30) {
      advice.push('🌱 植物需要施肥了！');
    }
    
    if (plantState.health < 50) {
      advice.push('💊 植物健康状况不佳，检查水分和营养');
    }
    
    if (plantState.water > 90) {
      advice.push('💧 水分充足，暂时不需要浇水');
    }
    
    if (plantState.health >= 80 && plantState.water >= 60 && plantState.nutrition >= 60) {
      advice.push('🌟 植物状态很好，继续保持！');
    }

    return advice;
  };

  return (
    <div className="controls">
      <div className="control-buttons">
        <button 
          className={`control-btn water-btn ${!canWater() ? 'disabled' : ''}`}
          onClick={onWater}
          disabled={!canWater()}
          title={canWater() ? '给植物浇水' : '水分已充足'}
        >
          <span className="btn-icon">💧</span>
          <span className="btn-text">浇水</span>
          {!canWater() && <span className="btn-status">已满</span>}
        </button>

        <button 
          className={`control-btn fertilize-btn ${!canFertilize() ? 'disabled' : ''}`}
          onClick={onFertilize}
          disabled={!canFertilize()}
          title={canFertilize() ? '给植物施肥' : '施肥间隔未到或营养充足'}
        >
          <span className="btn-icon">🌱</span>
          <span className="btn-text">施肥</span>
          {!canFertilize() && <span className="btn-status">等待</span>}
        </button>

        <button 
          className="control-btn details-btn"
          onClick={() => setShowDetails(!showDetails)}
        >
          <span className="btn-icon">📊</span>
          <span className="btn-text">详情</span>
        </button>
      </div>

      {showDetails && (
        <div className="plant-details">
          <div className="details-section">
            <h3>植物状态</h3>
            <div className="status-grid">
              <div className="status-item">
                <span className="status-icon">💚</span>
                <span className="status-name">健康度</span>
                <span className="status-value">{Math.round(plantState.health)}%</span>
              </div>
              <div className="status-item">
                <span className="status-icon">💧</span>
                <span className="status-name">水分</span>
                <span className="status-value">{Math.round(plantState.water)}%</span>
              </div>
              <div className="status-item">
                <span className="status-icon">🌱</span>
                <span className="status-name">营养</span>
                <span className="status-value">{Math.round(plantState.nutrition)}%</span>
              </div>
              <div className="status-item">
                <span className="status-icon">📈</span>
                <span className="status-name">生长</span>
                <span className="status-value">{Math.round(plantState.growth)}%</span>
              </div>
            </div>
          </div>

          <div className="details-section">
            <h3>护理记录</h3>
            <div className="care-history">
              <div className="care-item">
                <span className="care-icon">💧</span>
                <span className="care-text">上次浇水: {getTimeSinceLastAction(plantState.lastWatered)}</span>
              </div>
              <div className="care-item">
                <span className="care-icon">🌱</span>
                <span className="care-text">上次施肥: {getTimeSinceLastAction(plantState.lastFertilized)}</span>
              </div>
            </div>
          </div>

          <div className="details-section">
            <h3>护理建议</h3>
            <div className="advice-list">
              {getPlantAdvice().map((advice, index) => (
                <div key={index} className="advice-item">
                  {advice}
                </div>
              ))}
              {getPlantAdvice().length === 0 && (
                <div className="advice-item">
                  ✅ 暂无特别建议，植物状态良好
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Controls;
