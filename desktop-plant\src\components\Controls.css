.controls {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
}

.control-buttons {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.control-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.95);
  border: none;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  min-width: 70px;
}

.control-btn:hover:not(.disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.control-btn:active:not(.disabled) {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.control-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: rgba(200, 200, 200, 0.8);
}

.btn-icon {
  font-size: 24px;
  line-height: 1;
}

.btn-text {
  font-size: 12px;
  font-weight: 600;
  color: #333;
  line-height: 1;
}

.btn-status {
  position: absolute;
  top: -4px;
  right: -4px;
  background: #FF5722;
  color: white;
  font-size: 8px;
  padding: 2px 4px;
  border-radius: 6px;
  font-weight: 600;
}

/* 按钮特定样式 */
.water-btn:hover:not(.disabled) {
  background: rgba(33, 150, 243, 0.1);
  border: 1px solid rgba(33, 150, 243, 0.3);
}

.fertilize-btn:hover:not(.disabled) {
  background: rgba(76, 175, 80, 0.1);
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.details-btn:hover {
  background: rgba(156, 39, 176, 0.1);
  border: 1px solid rgba(156, 39, 176, 0.3);
}

/* 植物详情面板 */
.plant-details {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  min-width: 300px;
  max-width: 400px;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.details-section {
  margin-bottom: 20px;
}

.details-section:last-child {
  margin-bottom: 0;
}

.details-section h3 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 12px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 12px;
}

.status-icon {
  font-size: 16px;
}

.status-name {
  font-size: 12px;
  color: #666;
  flex: 1;
}

.status-value {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.care-history {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.care-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 12px;
}

.care-icon {
  font-size: 16px;
}

.care-text {
  font-size: 12px;
  color: #666;
}

.advice-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.advice-item {
  padding: 8px 12px;
  background: rgba(76, 175, 80, 0.1);
  border: 1px solid rgba(76, 175, 80, 0.2);
  border-radius: 12px;
  font-size: 12px;
  color: #2E7D32;
  line-height: 1.4;
}

/* 按钮点击效果 */
.control-btn:not(.disabled)::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
}

.control-btn:not(.disabled):active::before {
  width: 100px;
  height: 100px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .controls {
    bottom: 10px;
    left: 10px;
    right: 10px;
    transform: none;
  }
  
  .control-buttons {
    justify-content: center;
    gap: 8px;
  }
  
  .control-btn {
    padding: 10px 12px;
    min-width: 60px;
  }
  
  .btn-icon {
    font-size: 20px;
  }
  
  .btn-text {
    font-size: 11px;
  }
  
  .plant-details {
    min-width: auto;
    max-width: none;
    margin: 0 10px;
    padding: 16px;
  }
  
  .status-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .details-section h3 {
    font-size: 14px;
  }
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .control-btn {
    background: rgba(50, 50, 50, 0.9);
    color: #fff;
  }
  
  .btn-text {
    color: #fff;
  }
  
  .plant-details {
    background: rgba(30, 30, 30, 0.95);
    color: #fff;
  }
  
  .details-section h3 {
    color: #fff;
  }
  
  .status-item, .care-item {
    background: rgba(255, 255, 255, 0.1);
  }
  
  .status-name, .care-text {
    color: #ccc;
  }
  
  .status-value {
    color: #fff;
  }
}
