{"$schema": "https://schema.tauri.app/config/2", "productName": "desktop-plant", "version": "0.1.0", "identifier": "com.desktop-plant.app", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"withGlobalTauri": true, "windows": [{"title": "Desktop Plant", "width": 400, "height": 600, "resizable": true, "fullscreen": false, "transparent": true, "decorations": false, "alwaysOnTop": false, "skipTaskbar": true, "visible": true, "center": false, "x": 100, "y": 100, "minWidth": 300, "minHeight": 400, "maxWidth": 800, "maxHeight": 1000}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}