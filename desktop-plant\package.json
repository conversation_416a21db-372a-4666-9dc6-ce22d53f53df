{"name": "desktop-plant", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@tauri-apps/cli": "^2", "typescript": "~5.6.2", "vite": "^6.0.3"}}