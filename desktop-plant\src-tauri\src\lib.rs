use serde::{Deserialize, Serialize};
use std::collections::HashMap;

#[derive(Debug, Serialize, Deserialize)]
pub struct WeatherData {
    temperature: f32,
    humidity: f32,
    condition: String,
    location: String,
}

// 天气API命令
#[tauri::command]
async fn get_weather() -> Result<WeatherData, String> {
    // 这里应该调用真实的天气API，现在返回模拟数据
    // 你可以使用 reqwest 来调用真实的天气API

    // 模拟天气数据
    let weather = WeatherData {
        temperature: 22.5,
        humidity: 65.0,
        condition: "sunny".to_string(),
        location: "本地".to_string(),
    };

    Ok(weather)
}

// 系统信息命令
#[tauri::command]
fn get_system_info() -> Result<HashMap<String, String>, String> {
    let mut info = HashMap::new();

    // 获取系统时间
    let now = std::time::SystemTime::now()
        .duration_since(std::time::UNIX_EPOCH)
        .unwrap()
        .as_secs();

    info.insert("timestamp".to_string(), now.to_string());
    info.insert("platform".to_string(), std::env::consts::OS.to_string());

    Ok(info)
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .invoke_handler(tauri::generate_handler![get_weather, get_system_info])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
