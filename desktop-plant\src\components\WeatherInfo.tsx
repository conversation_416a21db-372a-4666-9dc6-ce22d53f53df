import React from 'react';
import './WeatherInfo.css';

interface WeatherData {
  temperature: number;
  humidity: number;
  condition: string;
  location: string;
}

interface WeatherInfoProps {
  weather: WeatherData | null;
}

const WeatherInfo: React.FC<WeatherInfoProps> = ({ weather }) => {
  if (!weather) {
    return (
      <div className="weather-info loading">
        <div className="weather-icon">🌤️</div>
        <div className="weather-text">加载天气中...</div>
      </div>
    );
  }

  const getWeatherIcon = (condition: string) => {
    switch (condition.toLowerCase()) {
      case 'sunny':
      case 'clear':
        return '☀️';
      case 'cloudy':
      case 'overcast':
        return '☁️';
      case 'rainy':
      case 'rain':
        return '🌧️';
      case 'snowy':
      case 'snow':
        return '❄️';
      case 'stormy':
      case 'thunderstorm':
        return '⛈️';
      case 'foggy':
      case 'mist':
        return '🌫️';
      default:
        return '🌤️';
    }
  };

  const getConditionText = (condition: string) => {
    switch (condition.toLowerCase()) {
      case 'sunny':
      case 'clear':
        return '晴朗';
      case 'cloudy':
      case 'overcast':
        return '多云';
      case 'rainy':
      case 'rain':
        return '下雨';
      case 'snowy':
      case 'snow':
        return '下雪';
      case 'stormy':
      case 'thunderstorm':
        return '雷暴';
      case 'foggy':
      case 'mist':
        return '雾霾';
      default:
        return '未知';
    }
  };

  const getTemperatureColor = (temp: number) => {
    if (temp >= 30) return '#FF6B6B';
    if (temp >= 25) return '#FF9F43';
    if (temp >= 20) return '#26de81';
    if (temp >= 15) return '#45aaf2';
    if (temp >= 10) return '#a55eea';
    return '#778ca3';
  };

  return (
    <div className="weather-info">
      <div className="weather-main">
        <div className="weather-icon">
          {getWeatherIcon(weather.condition)}
        </div>
        <div className="weather-details">
          <div 
            className="temperature"
            style={{ color: getTemperatureColor(weather.temperature) }}
          >
            {weather.temperature}°C
          </div>
          <div className="condition">
            {getConditionText(weather.condition)}
          </div>
        </div>
      </div>
      
      <div className="weather-secondary">
        <div className="humidity">
          <span className="humidity-icon">💧</span>
          <span className="humidity-value">{weather.humidity}%</span>
        </div>
        <div className="location">
          <span className="location-icon">📍</span>
          <span className="location-name">{weather.location}</span>
        </div>
      </div>

      {/* 天气对植物的影响提示 */}
      <div className="weather-impact">
        {weather.temperature >= 18 && weather.temperature <= 26 && (
          <div className="impact-good">🌱 温度适宜植物生长</div>
        )}
        {weather.temperature < 10 && (
          <div className="impact-bad">❄️ 温度过低，植物生长缓慢</div>
        )}
        {weather.temperature > 35 && (
          <div className="impact-bad">🔥 温度过高，需要更多水分</div>
        )}
        {weather.condition === 'rainy' && (
          <div className="impact-good">🌧️ 雨天增加空气湿度</div>
        )}
        {weather.condition === 'sunny' && (
          <div className="impact-good">☀️ 阳光充足，利于光合作用</div>
        )}
      </div>
    </div>
  );
};

export default WeatherInfo;
